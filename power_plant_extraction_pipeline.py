#!/usr/bin/env python3
"""
Complete Power Plant Data Extraction Pipeline
Extracts: Organizational Details → Plant Details → Unit Details

This pipeline implements the complete workflow:
1. Level 1: Search plant name → Top 5 links → Scrape → LLM fills org_details → Cache
2. Level 2: Use cache + targeted searches for missing plant_details fields → Cache
3. Level 3: Use cache + targeted searches for missing unit_details fields → Save JSONs

Usage: python power_plant_extraction_pipeline.py "Plant Name"
Example: python power_plant_extraction_pipeline.py "Jhajjar Power Plant"
"""
import asyncio
import json
import logging
import sys
import time
from datetime import datetime
from typing import Dict, Any, List, Tuple

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PowerPlantExtractionPipeline:
    """Complete three-level power plant extraction pipeline with intelligent caching and targeted searches."""

    def __init__(self, plant_name: str):
        """Initialize the pipeline for a specific plant."""
        self.plant_name = plant_name
        self.plant_safe_name = plant_name.lower().replace(" ", "_").replace("-", "_")

        # Initialize cache memory for all levels
        self.cache_memory = {
            'scraped_content': [],
            'org_details': {},
            'plant_details': {},
            'unit_details': []
        }

        # Initialize clients and extractors
        from src.config import config
        from src.enhanced_extractor import AdaptiveExtractor
        from src.plant_details_extractor import PlantDetailsExtractor
        from src.unit_details_extractor import UnitDetailsExtractor
        from src.field_analyzer import PlantFieldAnalyzer
        from src.cache_manager import plant_cache

        self.serp_api_key = config.pipeline.serp_api_key
        self.scraper_api_key = config.pipeline.scraper_api_key
        self.groq_api_key = config.pipeline.groq_api_key

        # Initialize extractors
        self.org_extractor = AdaptiveExtractor(
            groq_api_key=self.groq_api_key,
            use_bedrock=False,
            use_openai=False
        )
        # Initialize Groq client for plant extractor
        from src.groq_client import GroqExtractionClient
        groq_client = GroqExtractionClient(self.groq_api_key)
        self.plant_extractor = PlantDetailsExtractor(groq_client)
        self.unit_extractor = UnitDetailsExtractor()
        self.field_analyzer = PlantFieldAnalyzer()
        self.plant_cache = plant_cache

    async def run_complete_extraction(self) -> Tuple[Dict[str, Any], Dict[str, Any], List[Dict[str, Any]]]:
        """
        Run the complete three-level extraction pipeline following the exact workflow:
        1. Search plant name → Top 5 links → Scrape → LLM fills org_details → Cache
        2. Use cache + targeted searches for missing plant_details fields → Cache
        3. Use cache + targeted searches for missing unit_details fields → Save JSONs

        Returns:
            Tuple of (org_details, plant_details, unit_details_list)
        """
        print("🚀 INTELLIGENT POWER PLANT DATA EXTRACTION PIPELINE")
        print("=" * 70)
        print(f"🔍 Target Plant: {self.plant_name}")
        print(f"🧠 Method: Cache-Optimized + Targeted Missing Field Searches")
        print(f"📊 Workflow: Search → Cache → Intelligent Field Completion")
        print("=" * 70)

        start_time = time.time()

        # LEVEL 1: Organizational Details Extraction
        print("\n📊 LEVEL 1: ORGANIZATIONAL DETAILS EXTRACTION")
        print("-" * 50)
        org_details = await self._level_1_organizational_extraction()
        self._display_level_summary("Organizational", org_details, 1)

        # LEVEL 2: Plant Details Extraction with Intelligent Missing Field Search
        print("\n🏭 LEVEL 2: PLANT DETAILS EXTRACTION")
        print("-" * 50)
        plant_details = await self._level_2_plant_extraction()
        self._display_level_summary("Plant Technical", plant_details, 2)

        # LEVEL 3: Unit Details Extraction with Intelligent Missing Field Search
        print("\n⚡ LEVEL 3: UNIT DETAILS EXTRACTION")
        print("-" * 50)
        unit_details_list = await self._level_3_unit_extraction()
        self._display_unit_summary(unit_details_list)

        # Save only the final JSON results to workspace
        print("\n💾 SAVING FINAL JSON RESULTS TO WORKSPACE")
        print("-" * 50)
        await self._save_final_json_results(org_details, plant_details, unit_details_list)

        total_time = time.time() - start_time
        total_fields = self._count_total_fields(org_details, plant_details, unit_details_list)

        print(f"\n🎉 COMPLETE THREE-LEVEL EXTRACTION FINISHED!")
        print(f"⏱️  Total time: {total_time:.1f} seconds")
        print(f"📊 Total data points extracted: {total_fields}")
        print(f"🧠 Strategy: Intelligent cache + targeted missing field searches")
        print(f"💾 Cache efficiency: Reused data across all levels")

        return org_details, plant_details, unit_details_list

    async def _level_1_organizational_extraction(self) -> Dict[str, Any]:
        """
        LEVEL 1: Search plant name → Top 5 links → Scrape → LLM fills org_details → Cache
        """
        print("🔍 Step 1: Searching Google for plant name...")

        try:
            # Step 1: Search Google for plant name and get top 5 links
            from src.serp_client import SerpAPIClient
            from src.scraper_client import ScraperAPIClient

            print(f"   🔍 Searching Google for: {self.plant_name}")
            async with SerpAPIClient(self.serp_api_key) as serp_client:
                search_results = await serp_client.search(self.plant_name, num_results=5)
                print(f"   ✅ Found {len(search_results)} search results")

            # Step 2: Scrape content from top 5 links using ScraperAPI
            print("   📄 Step 2: Scraping content from top 5 links...")
            scraped_contents = []
            async with ScraperAPIClient(self.scraper_api_key) as scraper_client:
                for i, result in enumerate(search_results[:5]):
                    try:
                        print(f"      📄 Scraping page {i+1}/5: {result.title[:50]}...")
                        scraped_content = await scraper_client.scrape_url(result.url)
                        if scraped_content and scraped_content.content and len(scraped_content.content) > 200:
                            scraped_contents.append(scraped_content)
                        await asyncio.sleep(0.5)  # Rate limiting
                    except Exception as scrape_error:
                        logger.warning(f"Failed to scrape {result.url}: {scrape_error}")

            print(f"   ✅ Successfully scraped {len(scraped_contents)} pages")

            # Step 3: Combine all scraped data
            print("   🔗 Step 3: Combining all scraped data...")
            self.cache_memory['scraped_content'] = scraped_contents
            print(f"   💾 Cached {len(scraped_contents)} pages in memory for reuse")

            # Step 4: LLM fills org_details from combined content
            print("   🧠 Step 4: LLM processing content to fill org_details...")
            org_details = await self.org_extractor.extract_adaptively(scraped_contents, self.plant_name)

            # Convert to dict if it's a Pydantic model
            if hasattr(org_details, 'model_dump'):
                org_details = org_details.model_dump()

            # Step 5: Save org_details to cache memory
            self.cache_memory['org_details'] = org_details
            print("   💾 Saved org_details to cache memory")

            print("✅ Level 1 organizational extraction completed")
            return org_details

        except Exception as e:
            logger.error(f"Level 1 organizational extraction failed: {e}")
            print(f"❌ Level 1 organizational extraction failed: {e}")
            return {}

    async def _level_2_plant_extraction(self) -> Dict[str, Any]:
        """
        LEVEL 2: Use cache + targeted searches for missing plant_details fields → Cache
        Handle multiple plants if plant_count > 1 in org_details
        """
        print("🔍 Step 1: Using cached content for plant technical information...")

        try:
            # Get cached data
            cached_content = self.cache_memory['scraped_content']
            org_details = self.cache_memory['org_details']

            if not cached_content:
                print("   ⚠️  No cached content available, skipping plant extraction")
                return {}

            print(f"   💾 Using {len(cached_content)} cached pages from Level 1")

            # Check if multiple plants need to be processed
            plants_count = org_details.get('plants_count', 1) if org_details else 1
            print(f"   🏭 Processing {plants_count} plant(s) based on org_details")

            # Step 1: Extract plant details using cached content
            print("   🧠 Step 1: LLM processing cached content for plant_details...")
            # Convert org_details dict to object-like access if needed
            if isinstance(org_details, dict):
                class OrgDetailsObj:
                    def __init__(self, data):
                        for key, value in data.items():
                            setattr(self, key, value)
                org_details_obj = OrgDetailsObj(org_details)
            else:
                org_details_obj = org_details

            plant_details = await self.plant_extractor.extract_all_plant_details(
                cached_content, self.plant_name, org_details_obj
            )

            # Convert to dict if it's a Pydantic model
            if hasattr(plant_details, 'model_dump'):
                plant_details = plant_details.model_dump()
            elif plant_details is None:
                plant_details = {}

            # Step 2: Analyze missing fields in plant_details
            print("   🔍 Step 2: Analyzing missing fields in plant_details...")
            missing_fields = self._analyze_missing_plant_fields(plant_details)
            print(f"   📊 Found {len(missing_fields)} missing fields: {missing_fields}")

            # Step 3: Targeted searches for missing fields (including nested JSON)
            if missing_fields:
                print("   🎯 Step 3: Targeted Google searches for missing fields...")
                plant_details = await self._targeted_plant_field_search(plant_details, missing_fields)

            # Step 4: Save plant_details to cache memory
            self.cache_memory['plant_details'] = plant_details
            print("   💾 Saved plant_details to cache memory")

            print("✅ Level 2 plant extraction completed")
            return plant_details

        except Exception as e:
            logger.error(f"Level 2 plant extraction failed: {e}")
            print(f"❌ Level 2 plant extraction failed: {e}")
            return {}

    async def _level_3_unit_extraction(self) -> List[Dict[str, Any]]:
        """
        LEVEL 3: Use cache + targeted searches for missing unit_details fields → Save JSONs
        Extract based on units_id from plant_details
        """
        print("🔍 Step 1: Getting unit information from plant_details...")

        try:
            # Get cached data
            cached_content = self.cache_memory['scraped_content']
            plant_details = self.cache_memory['plant_details']

            # Get number of units from plant_details
            units_id = plant_details.get("units_id", [1, 2])
            if not units_id:
                units_id = [1, 2]  # Default assumption

            print(f"   🔢 Found {len(units_id)} units in plant_details: {units_id}")
            print(f"   💾 Using cached content from Level 1 & 2")

            unit_details_list = []

            for unit_id in units_id:
                print(f"\n   🔧 Processing Unit {unit_id}...")

                # Step 1: Extract unit details using cached content
                print(f"      🧠 Step 1: LLM processing cached content for Unit {unit_id}...")
                unit_details = await self._extract_unit_from_cache(unit_id, cached_content)

                # Step 2: Analyze missing fields in unit_details
                print(f"      🔍 Step 2: Analyzing missing fields for Unit {unit_id}...")
                missing_fields = self._analyze_missing_unit_fields(unit_details)
                print(f"      📊 Found {len(missing_fields)} missing fields")

                # Step 3: Targeted searches for missing unit fields
                if missing_fields:
                    print(f"      🎯 Step 3: Targeted searches for missing Unit {unit_id} fields...")
                    unit_details = await self._targeted_unit_field_search(unit_details, unit_id, missing_fields)

                unit_details_list.append(unit_details)
                print(f"   ✅ Unit {unit_id} details completed")

            # Save unit_details to cache memory
            self.cache_memory['unit_details'] = unit_details_list
            print("   💾 Saved unit_details to cache memory")

            print("✅ Level 3 unit extraction completed")
            return unit_details_list

        except Exception as e:
            logger.error(f"Level 3 unit extraction failed: {e}")
            print(f"❌ Level 3 unit extraction failed: {e}")
            return []

    async def _extract_single_unit_details_cached(self, unit_id: int, plant_details: Dict[str, Any]) -> Dict[str, Any]:
        """Extract details for a single unit using cached content."""
        try:
            from src.groq_client import GroqExtractionClient

            # Use cached content from Level 1 (no additional API calls)
            if not self.scraped_content_cache:
                print(f"      ⚠️  No cached content available for Unit {unit_id}")
                return self._create_empty_unit_structure(unit_id, plant_details)

            # Combine all cached content for unit extraction
            combined_content = "\n\n".join([
                content.get('content', '') for content in self.scraped_content_cache
            ])

            if not combined_content or len(combined_content) < 100:
                print(f"      ⚠️  Insufficient cached content for Unit {unit_id}")
                return self._create_empty_unit_structure(unit_id, plant_details)

            print(f"      💾 Using {len(self.scraped_content_cache)} cached pages for Unit {unit_id}")

            # Extract unit details using cached content
            groq_client = GroqExtractionClient(self.groq_api_key)

            # Create unit details structure
            unit_details = {
                "unit_id": unit_id,
                "unit_name": f"{self.plant_name} Unit {unit_id}",
                "plant_name": self.plant_name,
                "plant_id": plant_details.get("plant_id", 1),
                "specifications": await self._extract_unit_specifications_cached(groq_client, combined_content, unit_id),
                "operational_data": await self._extract_unit_operational_data_cached(groq_client, combined_content, unit_id),
                "environmental_data": await self._extract_unit_environmental_data_cached(groq_client, combined_content, unit_id),
                "unit_grid_connection": "",
                "unit_ppa_allocation": "",
                "operational_status": "operational",
                "expected_retirement_date": ""
            }

            return unit_details

        except Exception as e:
            logger.error(f"Cached unit extraction failed for Unit {unit_id}: {e}")
            return self._create_empty_unit_structure(unit_id, plant_details)

    def _create_empty_unit_structure(self, unit_id: int, plant_details: Dict[str, Any]) -> Dict[str, Any]:
        """Create empty unit structure when extraction fails."""
        return {
            "unit_id": unit_id,
            "unit_name": f"{self.plant_name} Unit {unit_id}",
            "plant_name": self.plant_name,
            "plant_id": plant_details.get("plant_id", 1),
            "specifications": {},
            "operational_data": {},
            "environmental_data": {},
            "unit_grid_connection": "",
            "unit_ppa_allocation": "",
            "operational_status": "",
            "expected_retirement_date": ""
        }

    async def _extract_unit_specifications_cached(self, groq_client, content: str, unit_id: int) -> Dict[str, Any]:
        """Extract unit technical specifications from cached content."""
        try:
            prompt = f"""
            From the following content about {self.plant_name}, extract technical specifications for Unit {unit_id}.
            Look for: capacity (MW), technology type, manufacturer, commissioning date, fuel type, efficiency.
            If Unit {unit_id} is not specifically mentioned, extract general plant specifications.

            Content: {content[:3000]}

            Return only a JSON object with these fields:
            {{
                "capacity_mw": "capacity in MW per unit or total/number of units",
                "technology": "technology type (coal, gas, nuclear, etc.)",
                "manufacturer": "equipment manufacturer",
                "commissioning_date": "YYYY-MM-DD or year",
                "fuel_type": "primary fuel type",
                "efficiency_percent": "efficiency percentage",
                "heat_rate": "heat rate value if available"
            }}
            """

            result = await groq_client.extract_with_prompt(prompt)
            if result and hasattr(result, 'extracted_value'):
                try:
                    return json.loads(result.extracted_value)
                except:
                    return {}
            return {}

        except Exception as e:
            logger.error(f"Unit specifications extraction failed: {e}")
            return {}

    async def _extract_unit_operational_data_cached(self, groq_client, content: str, unit_id: int) -> Dict[str, Any]:
        """Extract unit operational data from cached content."""
        try:
            prompt = f"""
            From the following content about {self.plant_name}, extract operational performance data for Unit {unit_id}.
            Look for: availability factor, capacity factor, annual generation, outage rates.
            If Unit {unit_id} is not specifically mentioned, extract general plant operational data.

            Content: {content[:3000]}

            Return only a JSON object with these fields:
            {{
                "availability_factor": "availability percentage",
                "capacity_factor": "capacity factor percentage",
                "annual_generation_gwh": "annual generation in GWh",
                "forced_outage_rate": "forced outage rate percentage",
                "planned_outage_days": "planned outage days per year"
            }}
            """

            result = await groq_client.extract_with_prompt(prompt)
            if result and hasattr(result, 'extracted_value'):
                try:
                    return json.loads(result.extracted_value)
                except:
                    return {}
            return {}

        except Exception as e:
            logger.error(f"Unit operational data extraction failed: {e}")
            return {}

    async def _extract_unit_environmental_data_cached(self, groq_client, content: str, unit_id: int) -> Dict[str, Any]:
        """Extract unit environmental data from cached content."""
        try:
            prompt = f"""
            From the following content about {self.plant_name}, extract environmental data for Unit {unit_id}.
            Look for: CO2 emissions, NOx emissions, SO2 emissions, water consumption, cooling system.
            If Unit {unit_id} is not specifically mentioned, extract general plant environmental data.

            Content: {content[:3000]}

            Return only a JSON object with these fields:
            {{
                "co2_emissions_tons_per_year": "CO2 emissions per year",
                "nox_emissions_mg_per_nm3": "NOx emissions",
                "so2_emissions_mg_per_nm3": "SO2 emissions",
                "water_consumption_m3_per_mwh": "water consumption per MWh",
                "cooling_system_type": "cooling system type"
            }}
            """

            result = await groq_client.extract_with_prompt(prompt)
            if result and hasattr(result, 'extracted_value'):
                try:
                    return json.loads(result.extracted_value)
                except:
                    return {}
            return {}

        except Exception as e:
            logger.error(f"Unit environmental data extraction failed: {e}")
            return {}

    def _analyze_missing_plant_fields(self, plant_details: Dict[str, Any]) -> List[str]:
        """Analyze which fields are missing in plant_details based on the exact JSON schema."""
        required_fields = [
            'name', 'plant_type', 'plant_address', 'lat', 'long',
            'plant_id', 'units_id', 'grid_connectivity_maps', 'ppa_details'
        ]

        missing_fields = []
        for field in required_fields:
            value = plant_details.get(field)
            if not value or value in [None, "", [], {}]:
                missing_fields.append(field)

        return missing_fields

    def _analyze_missing_unit_fields(self, unit_details: Dict[str, Any]) -> List[str]:
        """Analyze which fields are missing in unit_details based on the exact JSON schema."""
        # Key fields from the unit_details.json schema
        required_fields = [
            'unit_number', 'plant_id', 'capacity', 'capacity_unit', 'fuel_type',
            'technology', 'commencement_date', 'heat_rate', 'heat_rate_unit',
            'ppa_details', 'gross_power_generation', 'plf', 'PAF', 'emission_factor'
        ]

        missing_fields = []
        for field in required_fields:
            value = unit_details.get(field)
            if not value or value in [None, "", [], {}]:
                missing_fields.append(field)

        return missing_fields

    async def _targeted_plant_field_search(self, plant_details: Dict[str, Any], missing_fields: List[str]) -> Dict[str, Any]:
        """Perform targeted Google searches for missing plant fields."""
        try:
            from src.serp_client import SerpAPIClient
            from src.scraper_client import ScraperAPIClient
            from src.groq_client import GroqExtractionClient

            for field in missing_fields[:3]:  # Limit to top 3 missing fields
                try:
                    print(f"         🎯 Searching for missing field: {field}")

                    # Generate targeted query for missing field
                    query = self._generate_plant_field_query(field)
                    print(f"         🔍 Query: {query}")

                    # Search and scrape top 3 results
                    async with SerpAPIClient(self.serp_api_key) as serp_client:
                        search_results = await serp_client.search(query, num_results=3)

                    if search_results:
                        async with ScraperAPIClient(self.scraper_api_key) as scraper_client:
                            for result in search_results[:3]:
                                try:
                                    scraped_content = await scraper_client.scrape_url(result.url)
                                    if scraped_content and scraped_content.content:
                                        # Extract specific field using LLM
                                        groq_client = GroqExtractionClient(self.groq_api_key)
                                        field_value = await self._extract_specific_plant_field(
                                            groq_client, scraped_content.content, field
                                        )
                                        if field_value:
                                            plant_details[field] = field_value
                                            print(f"         ✅ Found value for {field}")
                                            break
                                except Exception as e:
                                    logger.warning(f"Failed to process result for {field}: {e}")

                    await asyncio.sleep(0.5)  # Rate limiting

                except Exception as e:
                    logger.warning(f"Failed to search for field {field}: {e}")

            return plant_details

        except Exception as e:
            logger.error(f"Targeted plant field search failed: {e}")
            return plant_details

    async def _targeted_unit_field_search(self, unit_details: Dict[str, Any], unit_id: int, missing_fields: List[str]) -> Dict[str, Any]:
        """Perform targeted Google searches for missing unit fields."""
        try:
            from src.serp_client import SerpAPIClient
            from src.scraper_client import ScraperAPIClient
            from src.groq_client import GroqExtractionClient

            for field in missing_fields[:3]:  # Limit to top 3 missing fields
                try:
                    print(f"            🎯 Searching for Unit {unit_id} missing field: {field}")

                    # Generate targeted query for missing unit field
                    query = self._generate_unit_field_query(unit_id, field)
                    print(f"            🔍 Query: {query}")

                    # Search and scrape top 3 results
                    async with SerpAPIClient(self.serp_api_key) as serp_client:
                        search_results = await serp_client.search(query, num_results=3)

                    if search_results:
                        async with ScraperAPIClient(self.scraper_api_key) as scraper_client:
                            for result in search_results[:3]:
                                try:
                                    scraped_content = await scraper_client.scrape_url(result.url)
                                    if scraped_content and scraped_content.content:
                                        # Extract specific field using LLM
                                        groq_client = GroqExtractionClient(self.groq_api_key)
                                        field_value = await self._extract_specific_unit_field(
                                            groq_client, scraped_content.content, unit_id, field
                                        )
                                        if field_value:
                                            if field in ['specifications', 'operational_data', 'environmental_data']:
                                                unit_details[field] = field_value
                                            else:
                                                unit_details[field] = field_value
                                            print(f"            ✅ Found value for Unit {unit_id} {field}")
                                            break
                                except Exception as e:
                                    logger.warning(f"Failed to process result for Unit {unit_id} {field}: {e}")

                    await asyncio.sleep(0.5)  # Rate limiting

                except Exception as e:
                    logger.warning(f"Failed to search for Unit {unit_id} field {field}: {e}")

            return unit_details

        except Exception as e:
            logger.error(f"Targeted unit field search failed: {e}")
            return unit_details

    def _generate_plant_field_query(self, field: str) -> str:
        """Generate targeted search query for missing plant field."""
        field_queries = {
            'lat': f"{self.plant_name} latitude coordinates GPS location",
            'long': f"{self.plant_name} longitude coordinates GPS location",
            'plant_address': f"{self.plant_name} address location",
            'grid_connectivity_maps': f"{self.plant_name} grid connection substation",
            'ppa_details': f"{self.plant_name} power purchase agreement PPA",
            'units_id': f"{self.plant_name} number of units capacity",
            'plant_type': f"{self.plant_name} technology type coal gas nuclear"
        }
        return field_queries.get(field, f"{self.plant_name} {field}")

    def _generate_unit_field_query(self, unit_id: int, field: str) -> str:
        """Generate targeted search query for missing unit field."""
        base_query = f"{self.plant_name} Unit {unit_id}"

        field_queries = {
            'specifications': f"{base_query} capacity MW technology manufacturer",
            'operational_data': f"{base_query} availability factor capacity factor generation",
            'environmental_data': f"{base_query} emissions CO2 NOx SO2 environmental"
        }
        return field_queries.get(field, f"{base_query} {field}")

    async def _extract_unit_from_cache(self, unit_id: int, cached_content: List) -> Dict[str, Any]:
        """Extract unit details from cached content based on the exact unit_details.json schema."""
        try:
            from src.groq_client import GroqExtractionClient

            # Combine cached content
            combined_content = "\n\n".join([
                content.content if hasattr(content, 'content') else str(content)
                for content in cached_content
            ])

            if not combined_content or len(combined_content) < 100:
                return self._create_empty_unit_structure(unit_id)

            # Extract unit details using LLM based on the exact schema
            groq_client = GroqExtractionClient(self.groq_api_key)

            # Create unit details structure matching the exact JSON schema
            unit_details = {
                "unit_number": unit_id,
                "plant_id": 1,
                "capacity": await self._extract_field_from_content(groq_client, combined_content, "capacity", unit_id),
                "capacity_unit": "MW",
                "fuel_type": await self._extract_field_from_content(groq_client, combined_content, "fuel_type", unit_id),
                "technology": await self._extract_field_from_content(groq_client, combined_content, "technology", unit_id),
                "commencement_date": await self._extract_field_from_content(groq_client, combined_content, "commencement_date", unit_id),
                "heat_rate": await self._extract_field_from_content(groq_client, combined_content, "heat_rate", unit_id),
                "heat_rate_unit": "kJ/kWh",
                "ppa_details": [],
                "gross_power_generation": [],
                "plf": [],
                "PAF": [],
                "emission_factor": [],
                "unit_efficiency": await self._extract_field_from_content(groq_client, combined_content, "unit_efficiency", unit_id),
                "unit_lifetime": await self._extract_field_from_content(groq_client, combined_content, "unit_lifetime", unit_id),
                "remaining_useful_life": "",
                "selected_coal_type": await self._extract_field_from_content(groq_client, combined_content, "selected_coal_type", unit_id),
                "boiler_type": await self._extract_field_from_content(groq_client, combined_content, "boiler_type", unit_id)
            }

            return unit_details

        except Exception as e:
            logger.error(f"Unit extraction from cache failed for Unit {unit_id}: {e}")
            return self._create_empty_unit_structure(unit_id)

    def _create_empty_unit_structure(self, unit_id: int) -> Dict[str, Any]:
        """Create empty unit structure based on the exact unit_details.json schema."""
        return {
            "unit_number": unit_id,
            "plant_id": 1,
            "capacity": "",
            "capacity_unit": "MW",
            "fuel_type": [],
            "technology": "",
            "commencement_date": "",
            "heat_rate": "",
            "heat_rate_unit": "kJ/kWh",
            "ppa_details": [],
            "gross_power_generation": [],
            "plf": [],
            "PAF": [],
            "emission_factor": [],
            "unit_efficiency": "",
            "unit_lifetime": "",
            "remaining_useful_life": "",
            "selected_coal_type": "",
            "boiler_type": ""
        }

    async def _extract_field_from_content(self, groq_client, content: str, field: str, unit_id: int) -> Any:
        """Extract specific field from content using LLM."""
        try:
            # Use the correct extract_field method from GroqExtractionClient
            result = await groq_client.extract_field(field, content, f"{self.plant_name} Unit {unit_id}")
            if result and hasattr(result, 'extracted_value'):
                return result.extracted_value or ""
            return ""

        except Exception as e:
            logger.error(f"Field extraction failed for {field}: {e}")
            return ""

    async def _extract_specific_plant_field(self, groq_client, content: str, field: str) -> Any:
        """Extract specific plant field using LLM."""
        try:
            # Use the correct extract_field method from GroqExtractionClient
            result = await groq_client.extract_field(field, content, self.plant_name)
            if result and hasattr(result, 'extracted_value'):
                return result.extracted_value
            return None

        except Exception as e:
            logger.error(f"Specific plant field extraction failed for {field}: {e}")
            return None

    async def _extract_specific_unit_field(self, groq_client, content: str, unit_id: int, field: str) -> Any:
        """Extract specific unit field using LLM."""
        try:
            # Use the correct extract_field method from GroqExtractionClient
            result = await groq_client.extract_field(field, content, f"{self.plant_name} Unit {unit_id}")
            if result and hasattr(result, 'extracted_value'):
                return result.extracted_value
            return None

        except Exception as e:
            logger.error(f"Specific unit field extraction failed for Unit {unit_id} {field}: {e}")
            return None

    def _display_level_summary(self, level_name: str, details: Dict[str, Any], level_num: int):
        """Display summary for a specific level."""
        if not details:
            print(f"❌ {level_name} extraction failed - no data retrieved")
            return

        filled_fields = sum(1 for v in details.values() if v not in [None, "", []])
        total_fields = len(details)
        print(f"📊 {level_name} Level {level_num}: {filled_fields}/{total_fields} fields extracted")

        # Show key fields based on level
        if level_num == 1:  # Organizational
            key_fields = ['organization_name', 'country_name', 'province', 'plant_types', 'cfpp_type']
        elif level_num == 2:  # Plant
            key_fields = ['name', 'plant_type', 'lat', 'long', 'units_id']
        else:
            key_fields = list(details.keys())[:5]  # First 5 fields

        for field in key_fields:
            value = details.get(field)
            if value and value not in [None, "", []]:
                if isinstance(value, list):
                    print(f"   • {field}: {', '.join(map(str, value))}")
                else:
                    display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    print(f"   • {field}: {display_value}")

    def _display_unit_summary(self, unit_details_list: List[Dict[str, Any]]):
        """Display unit details summary."""
        if not unit_details_list:
            print("❌ Unit extraction failed - no units retrieved")
            return

        print(f"⚡ Unit Level 3: {len(unit_details_list)} units extracted")

        for unit in unit_details_list:
            unit_id = unit.get('unit_id', 'Unknown')
            specs = unit.get('specifications', {})
            operational = unit.get('operational_data', {})

            print(f"\n   📋 Unit {unit_id}:")
            if specs.get('capacity_mw'):
                print(f"      • Capacity: {specs.get('capacity_mw')} MW")
            if specs.get('technology'):
                print(f"      • Technology: {specs.get('technology')}")
            if operational.get('availability_factor'):
                print(f"      • Availability: {operational.get('availability_factor')}%")

    async def _save_final_json_results(self, org_details: Dict[str, Any], plant_details: Dict[str, Any], unit_details_list: List[Dict[str, Any]]):
        """Save only the final JSON results to workspace as requested."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            # Save only the three required JSON files to workspace
            org_file = f"{self.plant_safe_name}_org_details_{timestamp}.json"
            with open(org_file, 'w', encoding='utf-8') as f:
                json.dump(org_details, f, indent=2, ensure_ascii=False)
            print(f"📊 org_details.json saved: {org_file}")

            plant_file = f"{self.plant_safe_name}_plant_details_{timestamp}.json"
            with open(plant_file, 'w', encoding='utf-8') as f:
                json.dump(plant_details, f, indent=2, ensure_ascii=False)
            print(f"🏭 plant_details.json saved: {plant_file}")

            units_file = f"{self.plant_safe_name}_unit_details_{timestamp}.json"
            with open(units_file, 'w', encoding='utf-8') as f:
                json.dump(unit_details_list, f, indent=2, ensure_ascii=False)
            print(f"⚡ unit_details.json saved: {units_file}")

            print(f"✅ All three level JSONs saved to workspace")

        except Exception as e:
            logger.error(f"Failed to save JSON results: {e}")
            print(f"❌ Failed to save JSON results: {e}")

    def _count_total_fields(self, org_details: Dict[str, Any], plant_details: Dict[str, Any], unit_details_list: List[Dict[str, Any]]) -> int:
        """Count total fields across all levels."""
        org_count = sum(1 for v in org_details.values() if v not in [None, "", []])
        plant_count = sum(1 for v in plant_details.values() if v not in [None, "", []])

        unit_count = 0
        for unit in unit_details_list:
            # Count main fields
            unit_count += sum(1 for v in unit.values() if v not in [None, "", []])
            # Count nested fields
            for nested_key in ['specifications', 'operational_data', 'environmental_data']:
                nested_dict = unit.get(nested_key, {})
                if isinstance(nested_dict, dict):
                    unit_count += sum(1 for v in nested_dict.values() if v not in [None, "", []])

        return org_count + plant_count + unit_count


async def main():
    """Main execution function."""
    if len(sys.argv) < 2:
        print("Usage: python power_plant_extraction_pipeline.py \"Plant Name\"")
        print("Example: python power_plant_extraction_pipeline.py \"Jhajjar Power Plant\"")
        print("Example: python power_plant_extraction_pipeline.py \"Vogtle Nuclear Plant\"")
        sys.exit(1)

    plant_name = sys.argv[1]

    print("🚀 INTELLIGENT POWER PLANT DATA EXTRACTION PIPELINE")
    print("Cache-optimized with targeted missing field searches")
    print()

    try:
        # Check API keys
        from src.config import config
        if not config.pipeline.serp_api_key:
            print("❌ SCRAPER_API_KEY not found in environment variables")
            sys.exit(1)

        if not config.pipeline.groq_api_key:
            print("❌ GROQ_API_KEY not found in environment variables")
            sys.exit(1)

        # Initialize and run intelligent pipeline
        pipeline = PowerPlantExtractionPipeline(plant_name)
        org_details, plant_details, unit_details_list = await pipeline.run_complete_extraction()

        # Display final results summary
        print(f"\n📄 EXTRACTION COMPLETED FOR: {plant_name}")
        print("=" * 70)
        print(f"📊 Organizational fields: {sum(1 for v in org_details.values() if v not in [None, '', []])}")
        print(f"🏭 Plant technical fields: {sum(1 for v in plant_details.values() if v not in [None, '', []])}")
        print(f"⚡ Unit details: {len(unit_details_list)} units with comprehensive specifications")
        print(f"🧠 Strategy: Intelligent cache + targeted missing field searches")
        print(f"💾 Efficiency: Level 1 cache → Level 2 cache → Level 3 targeted searches")
        print(f"✅ Complete workflow: Search → Cache → Intelligent Field Completion")

    except Exception as e:
        print(f"\n❌ EXTRACTION PIPELINE FAILED: {e}")
        logger.error(f"Pipeline failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
